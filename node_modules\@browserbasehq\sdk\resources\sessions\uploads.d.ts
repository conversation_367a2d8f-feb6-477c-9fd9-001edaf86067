import { APIResource } from "../../resource.js";
import * as Core from "../../core.js";
export declare class Uploads extends APIResource {
    /**
     * Create Session Uploads
     */
    create(id: string, body: UploadCreateParams, options?: Core.RequestOptions): Core.APIPromise<UploadCreateResponse>;
}
export interface UploadCreateResponse {
    message: string;
}
export interface UploadCreateParams {
    file: Core.Uploadable;
}
export declare namespace Uploads {
    export { type UploadCreateResponse as UploadCreateResponse, type UploadCreateParams as UploadCreateParams };
}
//# sourceMappingURL=uploads.d.ts.map
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../resource.mjs";
import * as Core from "../../core.mjs";
export class Uploads extends APIResource {
    /**
     * Create Session Uploads
     */
    create(id, body, options) {
        return this._client.post(`/v1/sessions/${id}/uploads`, Core.multipartFormRequestOptions({ body, ...options }));
    }
}
//# sourceMappingURL=uploads.mjs.map
{"level":"error","message":"Error performing similarity search: You must enter a `filter` object with at least one key-value pair.","name":"PineconeArgumentError","service":"mood-rag-chatbot","stack":"PineconeArgumentError: You must enter a `filter` object with at least one key-value pair.\n    at QueryCommand.validator (/home/<USER>/Desktop/<PERSON><PERSON>'s Bot/node_modules/@pinecone-database/pinecone/dist/data/vectors/query.js:33:27)\n    at QueryCommand.run (/home/<USER>/Desktop/<PERSON><PERSON>'s Bot/node_modules/@pinecone-database/pinecone/dist/data/vectors/query.js:58:14)\n    at Index.query (/home/<USER>/Desktop/<PERSON><PERSON>'s Bot/node_modules/@pinecone-database/pinecone/dist/data/index.js:395:41)\n    at PineconeStore._runPineconeQuery (/home/<USER>/Desktop/<PERSON><PERSON>'s Bot/node_modules/@langchain/pinecone/dist/vectorstores.cjs:333:41)\n    at PineconeStore.similaritySearchVectorWithScore (/home/<USER>/Desktop/<PERSON><PERSON>'s Bot/node_modules/@langchain/pinecone/dist/vectorstores.cjs:375:45)\n    at PineconeStore.similaritySearchWithScore (/home/<USER>/Desktop/Johaness's Bot/node_modules/@langchain/core/dist/vectorstores.cjs:280:21)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async VectorStoreService.similaritySearch (/home/<USER>/Desktop/Johaness's Bot/src/services/vectorStore.js:71:23)\n    at async VectorStoreSetup.verifySetup (/home/<USER>/Desktop/Johaness's Bot/scripts/setupVectorStore.js:140:23)\n    at async VectorStoreSetup.run (/home/<USER>/Desktop/Johaness's Bot/scripts/setupVectorStore.js:189:24)","timestamp":"2025-06-09T09:46:48.143Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at handleResponseNotOk (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:387:9)\n    at async generateContent (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:832:22)\n    at async GeminiConfig.generateText (/home/<USER>/Desktop/Johaness's Bot/src/config/gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (/home/<USER>/Desktop/Johaness's Bot/src/services/moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (/home/<USER>/Desktop/Johaness's Bot/src/services/moodAnalyzer.js:35:22)\n    at async ChatService.processMessage (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:60:28)\n    at async ChatbotCLI.handleInput (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:105:22)\n    at async Interface.<anonymous> (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:84:7)","status":404,"statusText":"Not Found","timestamp":"2025-06-09T09:53:47.201Z"}
{"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at handleResponseNotOk (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:387:9)\n    at async generateContent (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:832:22)\n    at async GeminiConfig.generateText (/home/<USER>/Desktop/Johaness's Bot/src/config/gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (/home/<USER>/Desktop/Johaness's Bot/src/services/moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (/home/<USER>/Desktop/Johaness's Bot/src/services/moodAnalyzer.js:35:22)\n    at async ChatService.processMessage (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:60:28)\n    at async ChatbotCLI.handleInput (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:105:22)\n    at async Interface.<anonymous> (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:84:7)","status":404,"statusText":"Not Found","timestamp":"2025-06-09T09:53:47.203Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at handleResponseNotOk (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:387:9)\n    at async generateContent (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:832:22)\n    at async GeminiConfig.generateText (/home/<USER>/Desktop/Johaness's Bot/src/config/gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (/home/<USER>/Desktop/Johaness's Bot/src/services/moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (/home/<USER>/Desktop/Johaness's Bot/src/services/moodAnalyzer.js:35:22)\n    at async ChatService.processMessage (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:60:28)\n    at async ChatbotCLI.handleInput (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:105:22)\n    at async Interface.<anonymous> (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:84:7)","status":404,"statusText":"Not Found","timestamp":"2025-06-09T09:53:48.109Z"}
{"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at handleResponseNotOk (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:387:9)\n    at async generateContent (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:832:22)\n    at async GeminiConfig.generateText (/home/<USER>/Desktop/Johaness's Bot/src/config/gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (/home/<USER>/Desktop/Johaness's Bot/src/services/moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (/home/<USER>/Desktop/Johaness's Bot/src/services/moodAnalyzer.js:35:22)\n    at async ChatService.processMessage (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:60:28)\n    at async ChatbotCLI.handleInput (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:105:22)\n    at async Interface.<anonymous> (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:84:7)","status":404,"statusText":"Not Found","timestamp":"2025-06-09T09:53:48.110Z"}
{"attemptNumber":1,"level":"error","message":"Error generating RAG response: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.","retriesLeft":6,"service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at handleResponseNotOk (/home/<USER>/Desktop/Johaness's Bot/node_modules/@langchain/google-genai/node_modules/@google/generative-ai/dist/index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/home/<USER>/Desktop/Johaness's Bot/node_modules/@langchain/google-genai/node_modules/@google/generative-ai/dist/index.js:403:9)\n    at async generateContent (/home/<USER>/Desktop/Johaness's Bot/node_modules/@langchain/google-genai/node_modules/@google/generative-ai/dist/index.js:867:22)\n    at async /home/<USER>/Desktop/Johaness's Bot/node_modules/@langchain/google-genai/dist/chat_models.cjs:725:24\n    at async RetryOperation._fn (/home/<USER>/Desktop/Johaness's Bot/node_modules/p-retry/index.js:50:12)","status":404,"statusText":"Not Found","timestamp":"2025-06-09T09:53:53.044Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at handleResponseNotOk (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:387:9)\n    at async generateContent (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:832:22)\n    at async GeminiConfig.generateText (/home/<USER>/Desktop/Johaness's Bot/src/config/gemini.js:59:22)\n    at async ChatService.generateFallbackResponse (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:247:24)\n    at async ChatService.generateRAGResponse (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:179:14)\n    at async ChatService.processMessage (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:78:20)\n    at async ChatbotCLI.handleInput (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:105:22)\n    at async Interface.<anonymous> (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:84:7)","status":404,"statusText":"Not Found","timestamp":"2025-06-09T09:53:54.079Z"}
{"level":"error","message":"Error generating fallback response: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at handleResponseNotOk (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:387:9)\n    at async generateContent (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:832:22)\n    at async GeminiConfig.generateText (/home/<USER>/Desktop/Johaness's Bot/src/config/gemini.js:59:22)\n    at async ChatService.generateFallbackResponse (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:247:24)\n    at async ChatService.generateRAGResponse (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:179:14)\n    at async ChatService.processMessage (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:78:20)\n    at async ChatbotCLI.handleInput (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:105:22)\n    at async Interface.<anonymous> (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:84:7)","status":404,"statusText":"Not Found","timestamp":"2025-06-09T09:53:54.080Z"}
{"attemptNumber":1,"level":"error","message":"Error generating RAG response: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.","retriesLeft":6,"service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at handleResponseNotOk (/home/<USER>/Desktop/Johaness's Bot/node_modules/@langchain/google-genai/node_modules/@google/generative-ai/dist/index.js:434:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/home/<USER>/Desktop/Johaness's Bot/node_modules/@langchain/google-genai/node_modules/@google/generative-ai/dist/index.js:403:9)\n    at async generateContent (/home/<USER>/Desktop/Johaness's Bot/node_modules/@langchain/google-genai/node_modules/@google/generative-ai/dist/index.js:867:22)\n    at async /home/<USER>/Desktop/Johaness's Bot/node_modules/@langchain/google-genai/dist/chat_models.cjs:725:24\n    at async RetryOperation._fn (/home/<USER>/Desktop/Johaness's Bot/node_modules/p-retry/index.js:50:12)","status":404,"statusText":"Not Found","timestamp":"2025-06-09T09:53:54.273Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at handleResponseNotOk (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:387:9)\n    at async generateContent (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:832:22)\n    at async GeminiConfig.generateText (/home/<USER>/Desktop/Johaness's Bot/src/config/gemini.js:59:22)\n    at async ChatService.generateFallbackResponse (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:247:24)\n    at async ChatService.generateRAGResponse (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:179:14)\n    at async ChatService.processMessage (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:78:20)\n    at async ChatbotCLI.handleInput (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:105:22)\n    at async Interface.<anonymous> (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:84:7)","status":404,"statusText":"Not Found","timestamp":"2025-06-09T09:53:55.516Z"}
{"level":"error","message":"Error generating fallback response: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent: [404 Not Found] models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.\n    at handleResponseNotOk (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:387:9)\n    at async generateContent (/home/<USER>/Desktop/Johaness's Bot/node_modules/@google/generative-ai/dist/index.js:832:22)\n    at async GeminiConfig.generateText (/home/<USER>/Desktop/Johaness's Bot/src/config/gemini.js:59:22)\n    at async ChatService.generateFallbackResponse (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:247:24)\n    at async ChatService.generateRAGResponse (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:179:14)\n    at async ChatService.processMessage (/home/<USER>/Desktop/Johaness's Bot/src/services/chatService.js:78:20)\n    at async ChatbotCLI.handleInput (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:105:22)\n    at async Interface.<anonymous> (/home/<USER>/Desktop/Johaness's Bot/scripts/testCli.js:84:7)","status":404,"statusText":"Not Found","timestamp":"2025-06-09T09:53:55.517Z"}
{"cause":{},"level":"error","message":"Error ensuring index exists: Request failed to reach Pinecone. This can occur for reasons such as network problems that prevent the request from being completed, or a Pinecone API outage. Check your network connection, and visit https://status.pinecone.io/ to see whether any outages are ongoing.","name":"PineconeConnectionError","service":"mood-rag-chatbot","stack":"PineconeConnectionError: Request failed to reach Pinecone. This can occur for reasons such as network problems that prevent the request from being completed, or a Pinecone API outage. Check your network connection, and visit https://status.pinecone.io/ to see whether any outages are ongoing.\n    at handleApiError (D:\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\errors\\handling.js:32:16)\n    at Object.onError (D:\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\utils\\middleware.js:71:59)\n    at BaseAPI.fetchApi (D:\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\pinecone-generated-ts-fetch\\db_control\\runtime.js:89:53)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ManageIndexesApi.request (D:\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\pinecone-generated-ts-fetch\\db_control\\runtime.js:152:26)\n    at async ManageIndexesApi.listIndexesRaw (D:\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\pinecone-generated-ts-fetch\\db_control\\apis\\ManageIndexesApi.js:512:26)\n    at async ManageIndexesApi.listIndexes (D:\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\pinecone-generated-ts-fetch\\db_control\\apis\\ManageIndexesApi.js:525:26)\n    at async Pinecone._listIndexes (D:\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\control\\listIndexes.js:6:26)\n    at async Pinecone.listIndexes (D:\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\pinecone.js:245:27)\n    at async PineconeConfig.ensureIndexExists (D:\\MoodifyMe Assistant\\src\\config\\database.js:38:25)","timestamp":"2025-06-10T11:19:37.322Z"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"mood-rag-chatbot","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\MoodifyMe Assistant\\node_modules\\express\\lib\\application.js:635:24)\n    at App.start (D:\\MoodifyMe Assistant\\src\\app.js:136:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-12T08:21:55.639Z"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"mood-rag-chatbot","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\MoodifyMe Assistant\\node_modules\\express\\lib\\application.js:635:24)\n    at App.start (D:\\MoodifyMe Assistant\\src\\app.js:136:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-12T08:22:31.497Z"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3000","port":3000,"service":"mood-rag-chatbot","stack":"Error: listen EADDRINUSE: address already in use :::3000\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\express\\lib\\application.js:635:24)\n    at App.start (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\app.js:139:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-12T09:30:08.877Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async ChatService.processMessage (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:60:28)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:20:20","timestamp":"2025-06-12T12:13:21.639Z"}
{"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async ChatService.processMessage (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:60:28)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:20:20","timestamp":"2025-06-12T12:13:21.645Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async JokeService.generateMoodBasedResponse (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\jokeService.js:187:24)\n    at async ChatService.generateJokeResponse (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:130:28)\n    at async ChatService.processMessage (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:76:20)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:20:20","timestamp":"2025-06-12T12:13:22.168Z"}
{"level":"error","message":"Error generating mood-based response: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async JokeService.generateMoodBasedResponse (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\jokeService.js:187:24)\n    at async ChatService.generateJokeResponse (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:130:28)\n    at async ChatService.processMessage (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:76:20)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:20:20","timestamp":"2025-06-12T12:13:22.174Z"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"mood-rag-chatbot","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\express\\lib\\application.js:635:24)\n    at App.start (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\app.js:139:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-15T10:00:15.094Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"48s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:14.120Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"48s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:14.136Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"48s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:14.954Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"48s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:14.969Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"48s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:14.988Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"48s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:15.003Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"48s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:15.028Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"48s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:15.044Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"48s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:15.061Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"48s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"48s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:15.069Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"47s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"47s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"47s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:15.754Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"47s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"47s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"47s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:15.770Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"46s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:16.536Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"46s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:16.551Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"46s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:16.572Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"46s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:16.587Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"46s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:16.619Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"46s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:16.641Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"46s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:16.662Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"46s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:16.670Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"46s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:16.883Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"46s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"46s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:16.898Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"45s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"45s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"45s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:17.086Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"45s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"45s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"45s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:17.101Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"45s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"45s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"45s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:17.904Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"45s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"45s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"45s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:17.920Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:18.111Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:18.126Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:18.197Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:18.216Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:18.504Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:18.522Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:18.748Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:18.764Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:18.853Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"44s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"44s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:18.863Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"43s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:19.124Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"43s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:19.141Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"43s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:19.177Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"43s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:19.189Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"43s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:19.582Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"43s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:19.598Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"43s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:19.790Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"43s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"43s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:19.808Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"42s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"42s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"42s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:20.261Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"42s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"42s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"42s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:20.278Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"42s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"42s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"42s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:20.444Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"42s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"42s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"42s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:20.459Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"41s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.059Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"41s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.075Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"41s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.094Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"41s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.111Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"42s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"42s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"42s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.143Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"42s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"42s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"42s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.154Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"41s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.225Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"41s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.240Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"41s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.534Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"41s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.550Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"41s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.700Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"41s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.720Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"41s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.801Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"41s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"41s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:21.818Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"40s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:22.305Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"40s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:22.322Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"40s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:22.348Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"40s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:22.362Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"40s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:22.402Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"40s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:22.419Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"40s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:22.999Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"40s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"40s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:23.015Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"39s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:23.039Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"39s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:23.053Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"39s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:23.329Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"39s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:23.347Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"39s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:23.617Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"39s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:23.632Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"39s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:23.710Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"39s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"39s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:23.727Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"38s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"38s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"38s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:24.122Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"38s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"38s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"38s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:24.129Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"38s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"38s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"38s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:24.745Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"38s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"38s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"38s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:24.762Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"38s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"38s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"38s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:24.898Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"38s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"38s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"38s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:24.914Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"37s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"37s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"37s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:24.997Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"37s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"37s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"37s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:25.012Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"37s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"37s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"37s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:25.299Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"37s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"37s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"37s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:25.317Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"37s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"37s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"37s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:25.537Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"37s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"37s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"37s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:25.556Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"36s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"36s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"36s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:25.955Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"36s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"36s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"36s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:25.971Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"36s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"36s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"36s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:26.017Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"36s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"36s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"model\":\"gemini-1.5-flash\",\"location\":\"global\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"36s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:26.031Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"36s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"36s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"36s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:26.183Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"36s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"36s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"36s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:26.199Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"35s"}],"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"35s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"35s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async ChatService.processMessage (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:67:28)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:20:20","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:27.532Z"}
{"errorDetails":[{"@type":"type.googleapis.com/google.rpc.QuotaFailure","violations":[{"quotaDimensions":{"location":"global","model":"gemini-1.5-flash"},"quotaId":"GenerateRequestsPerMinutePerProjectPerModel-FreeTier","quotaMetric":"generativelanguage.googleapis.com/generate_content_free_tier_requests","quotaValue":"15"}]},{"@type":"type.googleapis.com/google.rpc.Help","links":[{"description":"Learn more about Gemini API quotas","url":"https://ai.google.dev/gemini-api/docs/rate-limits"}]},{"@type":"type.googleapis.com/google.rpc.RetryInfo","retryDelay":"35s"}],"level":"error","message":"Error in AI mood analysis: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"35s\"}]","service":"mood-rag-chatbot","stack":"Error: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent: [429 Too Many Requests] You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [{\"@type\":\"type.googleapis.com/google.rpc.QuotaFailure\",\"violations\":[{\"quotaMetric\":\"generativelanguage.googleapis.com/generate_content_free_tier_requests\",\"quotaId\":\"GenerateRequestsPerMinutePerProjectPerModel-FreeTier\",\"quotaDimensions\":{\"location\":\"global\",\"model\":\"gemini-1.5-flash\"},\"quotaValue\":\"15\"}]},{\"@type\":\"type.googleapis.com/google.rpc.Help\",\"links\":[{\"description\":\"Learn more about Gemini API quotas\",\"url\":\"https://ai.google.dev/gemini-api/docs/rate-limits\"}]},{\"@type\":\"type.googleapis.com/google.rpc.RetryInfo\",\"retryDelay\":\"35s\"}]\n    at handleResponseNotOk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:414:11)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:387:9)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:59:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async ChatService.processMessage (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:67:28)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:20:20","status":429,"statusText":"Too Many Requests","timestamp":"2025-06-15T10:10:27.547Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:13.604Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:13.614Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:13.622Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:13.627Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:13.632Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:13.635Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:18.154Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:18.159Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:18.170Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:18.175Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:18.185Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:18.189Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:24.221Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:24.227Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:24.240Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:24.246Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:24.253Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:24.257Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:28.868Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:28.869Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:28.871Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:28.873Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:28.875Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:45:28.876Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:15.614Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:15.621Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:15.633Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:15.636Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:15.644Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:15.646Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:20.142Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:20.148Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:20.160Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:20.166Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:20.173Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:20.180Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:26.205Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:26.211Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:26.219Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:26.222Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:26.226Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:26.228Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:30.753Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:30.760Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:30.768Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at runNextTicks (node:internal/process/task_queues:69:3)\n    at process.processImmediate (node:internal/timers:453:9)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:30.771Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:30.775Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:46:30.777Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:47:07.666Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:47:07.672Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:47:07.684Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:47:07.691Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:97:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:47:07.702Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:114:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:139:26","timestamp":"2025-06-15T10:47:07.705Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:110:22)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async ChatService.processMessage (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:67:28)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:20:20","timestamp":"2025-06-15T11:46:40.480Z"}
{"level":"error","message":"Error in AI mood analysis: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:132:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async MoodAnalyzer.analyzeWithAI (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:96:24)\n    at async MoodAnalyzer.analyzeMood (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\moodAnalyzer.js:35:22)\n    at async ChatService.processMessage (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:67:28)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:20:20","timestamp":"2025-06-15T11:46:40.487Z"}
{"level":"error","message":"Error generating text with Gemini: [GoogleGenerativeAI Error]: Error fetching from https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent: fetch failed","service":"mood-rag-chatbot","stack":"TypeError: fetch failed\n    at node:internal/deps/undici/undici:13510:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async makeRequest (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:381:20)\n    at async generateContent (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@google\\generative-ai\\dist\\index.js:832:22)\n    at async GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:110:22)\n    at async JokeService.generateMoodBasedResponse (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\jokeService.js:187:24)\n    at async ChatService.generateJokeResponse (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:174:28)\n    at async ChatService.processMessage (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:91:20)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:20:20","timestamp":"2025-06-15T11:46:41.495Z"}
{"level":"error","message":"Error generating mood-based response: Network connection error. Please check your internet connection and try again.","service":"mood-rag-chatbot","stack":"Error: Network connection error. Please check your internet connection and try again.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:132:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async JokeService.generateMoodBasedResponse (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\jokeService.js:187:24)\n    at async ChatService.generateJokeResponse (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:174:28)\n    at async ChatService.processMessage (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\chatService.js:91:20)\n    at async D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\routes\\chat.js:20:20","timestamp":"2025-06-15T11:46:41.501Z"}
{"level":"error","message":"CRISIS INTERVENTION TRIGGERED: severe_depression (medium severity) - User message: feeling sad...","service":"mood-rag-chatbot","timestamp":"2025-06-15T11:47:10.422Z"}
{"level":"error","message":"CRISIS INTERVENTION TRIGGERED: severe_depression (medium severity) - User message: i am feeling sad...","service":"mood-rag-chatbot","timestamp":"2025-06-15T11:47:19.621Z"}
{"level":"error","message":"CRISIS INTERVENTION TRIGGERED: severe_depression (medium severity) - User message: I'm currently feeling sad and I want to feel happy. Can you help me with this emotional transition?...","service":"mood-rag-chatbot","timestamp":"2025-06-15T12:02:13.416Z"}
{"level":"error","message":"Error generating text with Gemini: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.getModel (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:62:13)\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:109:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:64:26)","timestamp":"2025-06-25T18:48:37.933Z"}
{"level":"error","message":"Error getting AI risk assessment: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:136:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:64:26)","timestamp":"2025-06-25T18:48:37.936Z"}
{"level":"error","message":"Error generating text with Gemini: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.getModel (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:62:13)\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:109:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:64:26)","timestamp":"2025-06-25T18:48:37.938Z"}
{"level":"error","message":"Error getting AI risk assessment: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:136:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:64:26)","timestamp":"2025-06-25T18:48:37.939Z"}
{"level":"error","message":"Error generating text with Gemini: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.getModel (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:62:13)\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:109:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:64:26)","timestamp":"2025-06-25T18:48:37.940Z"}
{"level":"error","message":"Error getting AI risk assessment: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:136:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:64:26)","timestamp":"2025-06-25T18:48:37.941Z"}
{"level":"error","message":"Error generating text with Gemini: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.getModel (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:62:13)\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:109:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:64:26)","timestamp":"2025-06-25T18:48:37.943Z"}
{"level":"error","message":"Error getting AI risk assessment: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:136:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:64:26)","timestamp":"2025-06-25T18:48:37.943Z"}
{"level":"error","message":"Error generating text with Gemini: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.getModel (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:62:13)\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:109:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:64:26)","timestamp":"2025-06-25T18:48:37.945Z"}
{"level":"error","message":"Error getting AI risk assessment: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:136:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:64:26)","timestamp":"2025-06-25T18:48:37.946Z"}
{"level":"error","message":"Error generating text with Gemini: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.getModel (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:62:13)\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:109:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:64:26)","timestamp":"2025-06-25T18:48:37.947Z"}
{"level":"error","message":"Error getting AI risk assessment: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:136:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:64:26)","timestamp":"2025-06-25T18:48:37.948Z"}
{"level":"error","message":"Error generating text with Gemini: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.getModel (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:62:13)\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:109:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:184:30)","timestamp":"2025-06-25T18:48:37.953Z"}
{"level":"error","message":"Error getting AI risk assessment: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.","service":"mood-rag-chatbot","stack":"Error: AI service temporarily unavailable: Gemini model not initialized. Call initialize() first.\n    at GeminiConfig.generateText (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\config\\gemini.js:136:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CrisisDetectionService.getAIRiskAssessment (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:272:24)\n    at async CrisisDetectionService.assessCrisisRisk (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\services\\crisisDetection.js:77:26)\n    at async testCrisisSystem (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\scripts\\testCrisisSystem.js:184:30)","timestamp":"2025-06-25T18:48:37.953Z"}
{"attemptNumber":7,"cause":{},"level":"error","message":"Error adding documents to vector store: Request failed to reach Pinecone. This can occur for reasons such as network problems that prevent the request from being completed, or a Pinecone API outage. Check your network connection, and visit https://status.pinecone.io/ to see whether any outages are ongoing.","name":"PineconeConnectionError","retriesLeft":0,"service":"mood-rag-chatbot","stack":"PineconeConnectionError: Request failed to reach Pinecone. This can occur for reasons such as network problems that prevent the request from being completed, or a Pinecone API outage. Check your network connection, and visit https://status.pinecone.io/ to see whether any outages are ongoing.\n    at handleApiError (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\errors\\handling.js:32:16)\n    at Object.onError (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\utils\\middleware.js:71:59)\n    at BaseAPI.fetchApi (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\pinecone-generated-ts-fetch\\db_data\\runtime.js:89:53)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async VectorOperationsApi.request (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\pinecone-generated-ts-fetch\\db_data\\runtime.js:152:26)\n    at async VectorOperationsApi.upsertVectorsRaw (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\pinecone-generated-ts-fetch\\db_data\\apis\\VectorOperationsApi.js:324:26)\n    at async VectorOperationsApi.upsertVectors (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\pinecone-generated-ts-fetch\\db_data\\apis\\VectorOperationsApi.js:338:26)\n    at async RetryOnServerFailure.execute (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\utils\\retries.js:48:34)\n    at async UpsertCommand.run (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\data\\vectors\\upsert.js:33:9)\n    at async Index.upsert (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\data\\index.js:351:16)","timestamp":"2025-06-25T19:04:33.003Z"}
{"attemptNumber":7,"cause":{},"level":"error","message":"Error adding documents to vector store: Request failed to reach Pinecone. This can occur for reasons such as network problems that prevent the request from being completed, or a Pinecone API outage. Check your network connection, and visit https://status.pinecone.io/ to see whether any outages are ongoing.","name":"PineconeConnectionError","retriesLeft":0,"service":"mood-rag-chatbot","stack":"PineconeConnectionError: Request failed to reach Pinecone. This can occur for reasons such as network problems that prevent the request from being completed, or a Pinecone API outage. Check your network connection, and visit https://status.pinecone.io/ to see whether any outages are ongoing.\n    at handleApiError (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\errors\\handling.js:32:16)\n    at Object.onError (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\utils\\middleware.js:71:59)\n    at BaseAPI.fetchApi (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\pinecone-generated-ts-fetch\\db_data\\runtime.js:89:53)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async VectorOperationsApi.request (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\pinecone-generated-ts-fetch\\db_data\\runtime.js:152:26)\n    at async VectorOperationsApi.upsertVectorsRaw (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\pinecone-generated-ts-fetch\\db_data\\apis\\VectorOperationsApi.js:324:26)\n    at async VectorOperationsApi.upsertVectors (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\pinecone-generated-ts-fetch\\db_data\\apis\\VectorOperationsApi.js:338:26)\n    at async RetryOnServerFailure.execute (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\utils\\retries.js:48:34)\n    at async UpsertCommand.run (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\data\\vectors\\upsert.js:33:9)\n    at async Index.upsert (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\@pinecone-database\\pinecone\\dist\\data\\index.js:351:16)","timestamp":"2025-06-25T19:12:10.742Z"}
{"level":"error","message":"CRISIS INTERVENTION TRIGGERED: severe_depression (medium severity) - User message: I'm currently feeling sad and I want to feel focused. Can you help me with this emotional transition...","service":"mood-rag-chatbot","timestamp":"2025-06-27T06:37:36.679Z"}
{"level":"error","message":"CRISIS INTERVENTION TRIGGERED: severe_depression (medium severity) - User message: I'm currently feeling sad and I want to feel ambitious. Can you help me with this emotional transiti...","service":"mood-rag-chatbot","timestamp":"2025-06-27T06:38:41.399Z"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::3001","port":3001,"service":"mood-rag-chatbot","stack":"Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\node_modules\\express\\lib\\application.js:635:24)\n    at App.start (D:\\Xampp\\htdocs\\MoodifyMe\\MoodifyMe Assistant\\src\\app.js:139:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-06-28T09:41:11.715Z"}
